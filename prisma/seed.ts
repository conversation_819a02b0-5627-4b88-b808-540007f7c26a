import { PrismaClient } from '../src/generated/prisma';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Create user roles
  const bossRole = await prisma.userRole.upsert({
    where: { id: 1 },
    update: {
      isOwner: true,
      isAdmin: true,
      isMember: false,
      isBot: false,
    },
    create: {
      name: 'Boss',
      description: 'Organization owner with full administrative privileges',
      isOwner: true,
      isAdmin: true,
      isMember: false,
      isBot: false,
    },
  });
  const adminRole = await prisma.userRole.upsert({
    where: { id: 2 },
    update: {
      isOwner: false,
      isAdmin: true,
      isMember: false,
      isBot: false,
    },
    create: {
      name: 'Admin',
      description: 'Organization admin with administrative privileges',
      isOwner: false,
      isAdmin: true,
      isMember: false,
      isBot: false,
    },
  });
  const memberRole = await prisma.userRole.upsert({
    where: { id: 3 },
    update: {
      isOwner: false,
      isAdmin: false,
      isMember: true,
      isBot: false,
    },
    create: {
      name: 'Member',
      description: 'Regular organization member',
      isOwner: false,
      isAdmin: false,
      isMember: true,
      isBot: false,
    },
  });
  const botRole = await prisma.userRole.upsert({
    where: { id: 4 },
    update: {
      isOwner: false,
      isAdmin: false,
      isMember: false,
      isBot: true,
    },
    create: {
      name: 'Bot',
      description: 'Automated bot user with limited privileges',
      isOwner: false,
      isAdmin: false,
      isMember: false,
      isBot: true,
    },
  });

  console.log({ bossRole, adminRole, memberRole, botRole });

  // Create task statuses
  const taskStatuses = [
    {
      id: 1,
      name: 'todo',
      displayName: 'To Do',
      color: '#E5E7EB',
      description: 'Task has been created but work has not started',
      index: 1,
      isMemberDisplay: true,
    },
    {
      id: 2,
      name: 'in-progress',
      displayName: 'In Progress',
      color: '#60A5FA',
      description: 'Work on the task is currently in progress',
      index: 2,
      isMemberDisplay: true,
    },
    {
      id: 3,
      name: 'completed',
      displayName: 'Completed',
      color: '#34D399',
      description: 'Task has been completed successfully',
      index: 4,
      isMemberDisplay: true,
    },
    {
      id: 4,
      name: 'cancel',
      displayName: 'Cancelled',
      color: '#F87171',
      description: 'Task has been cancelled',
      index: 5,
      isMemberDisplay: true, // Members don't need to see cancelled tasks in their status options
    },
    {
      id: 5,
      name: 'wait_feedback',
      displayName: 'Waiting for Feedback',
      color: '#FBBF24',
      description: 'Task is waiting for feedback or review',
      index: 3,
      isMemberDisplay: true,
    },
    {
      id: 6,
      name: 'on_backlog',
      displayName: 'Backlog',
      color: '#A78BFA',
      description: 'Task is in the backlog for future consideration',
      index: 0,
      isMemberDisplay: true, // Only admins/owners manage the backlog
    },
  ];

  const createdTaskStatuses = [];

  for (const status of taskStatuses) {
    const createdStatus = await prisma.taskStatus.upsert({
      where: { id: status.id },
      update: {
        index: status.index,
        isMemberDisplay: status.isMemberDisplay,
      },
      create: status,
    });
    createdTaskStatuses.push(createdStatus);
  }

  console.log('Created task statuses:', createdTaskStatuses);

  // Create task progress types
  const taskProgressTypes = [
    {
      id: 1,
      name: 'comment',
      displayName: 'Comment',
      color: '#3B82F6',
      description: 'General comment on the task',
      isOwner: true, // Owner can see and use this progress type
      isAdmin: true, // Admin can see and use this progress type
      isMember: true, // Member can see and use this progress type
    },
    {
      id: 2,
      name: 'request',
      displayName: 'Request',
      color: '#F59E0B',
      description: 'Request for information or resources related to the task',
      isOwner: true, // Owner can see and use this progress type
      isAdmin: true, // Admin can see and use this progress type
      isMember: true, // Member can make requests
    },
    {
      id: 3,
      name: 'log',
      displayName: 'Activity Log',
      color: '#8B5CF6',
      description: 'Log of activity or work done on the task',
      isOwner: false, // Owner can see and use this progress type
      isAdmin: false, // Admin can see and use this progress type
      isMember: false, // Member can log their activity
    },
  ];

  const createdTaskProgressTypes = [];

  for (const progressType of taskProgressTypes) {
    const createdProgressType = await prisma.taskProgressType.upsert({
      where: { id: progressType.id },
      update: {},
      create: progressType,
    });
    createdTaskProgressTypes.push(createdProgressType);
  }

  console.log('Created task progress types:', createdTaskProgressTypes);

  // Create task status transitions
  // Status IDs:
  // 1: ToDo
  // 2: In Progress
  // 3: Completed
  // 4: Cancelled
  // 5: Waiting for Feedback
  // 6: Backlog

  const taskStatusTransitions = [
    // ToDo → InProgress (All roles can do this)
    {
      id: 1,
      fromStatusId: 1, // ToDo
      toStatusId: 2, // InProgress
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Start working on a task',
    },
    // ToDo → Completed (Only Owner can do this)
    {
      id: 2,
      fromStatusId: 1, // ToDo
      toStatusId: 3, // Completed
      allowOwner: true,
      allowAdmin: false,
      allowMember: false,
      description: 'Mark a To Do task as completed',
    },
    // ToDo → Cancelled (All roles can do this)
    {
      id: 3,
      fromStatusId: 1, // ToDo
      toStatusId: 4, // Cancelled
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Cancel a task that has not been started',
    },
    // ToDo → WaitForFeedback (All roles can do this)
    {
      id: 4,
      fromStatusId: 1, // ToDo
      toStatusId: 5, // WaitForFeedback
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Request feedback on a task that has not been started',
    },
    // ToDo → Backlog (All roles can do this)
    {
      id: 5,
      fromStatusId: 1, // ToDo
      toStatusId: 6, // Backlog
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move task to backlog',
    },
    // InProgress → ToDo (All roles can do this)
    {
      id: 6,
      fromStatusId: 2, // InProgress
      toStatusId: 1, // ToDo
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move task back to To Do',
    },
    // InProgress → Completed (Only Owner can do this)
    {
      id: 7,
      fromStatusId: 2, // InProgress
      toStatusId: 3, // Completed
      allowOwner: true,
      allowAdmin: false,
      allowMember: false,
      description: 'Mark an in-progress task as completed',
    },
    // InProgress → Cancelled (All roles can do this)
    {
      id: 8,
      fromStatusId: 2, // InProgress
      toStatusId: 4, // Cancelled
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Cancel a task that is in progress',
    },
    // InProgress → WaitForFeedback (All roles can do this)
    {
      id: 9,
      fromStatusId: 2, // InProgress
      toStatusId: 5, // WaitForFeedback
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Request feedback on a task in progress',
    },
    // InProgress → Backlog (All roles can do this)
    {
      id: 10,
      fromStatusId: 2, // InProgress
      toStatusId: 6, // Backlog
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move in-progress task to backlog',
    },
    // Completed → ToDo (All roles can do this - for reopening tasks)
    {
      id: 11,
      fromStatusId: 3, // Completed
      toStatusId: 1, // ToDo
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Reopen a completed task',
    },
    // Completed → InProgress (All roles can do this - for reopening tasks)
    {
      id: 12,
      fromStatusId: 3, // Completed
      toStatusId: 2, // InProgress
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Reopen a completed task and start working on it',
    },
    // Completed → Cancelled (All roles can do this)
    {
      id: 13,
      fromStatusId: 3, // Completed
      toStatusId: 4, // Cancelled
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Cancel a completed task',
    },
    // Completed → WaitForFeedback (All roles can do this)
    {
      id: 14,
      fromStatusId: 3, // Completed
      toStatusId: 5, // WaitForFeedback
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move completed task to waiting for feedback',
    },
    // Completed → Backlog (All roles can do this)
    {
      id: 15,
      fromStatusId: 3, // Completed
      toStatusId: 6, // Backlog
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move completed task to backlog',
    },
    // Cancelled → ToDo (All roles can do this - for reopening cancelled tasks)
    {
      id: 16,
      fromStatusId: 4, // Cancelled
      toStatusId: 1, // ToDo
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Reopen a cancelled task',
    },
    // Cancelled → InProgress (All roles can do this)
    {
      id: 17,
      fromStatusId: 4, // Cancelled
      toStatusId: 2, // InProgress
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Reopen a cancelled task and start working on it',
    },
    // Cancelled → Completed (Only Owner can do this)
    {
      id: 18,
      fromStatusId: 4, // Cancelled
      toStatusId: 3, // Completed
      allowOwner: true,
      allowAdmin: false,
      allowMember: false,
      description: 'Mark a cancelled task as completed',
    },
    // Cancelled → WaitForFeedback (All roles can do this)
    {
      id: 19,
      fromStatusId: 4, // Cancelled
      toStatusId: 5, // WaitForFeedback
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Reopen a cancelled task for feedback',
    },
    // Cancelled → Backlog (All roles can do this)
    {
      id: 20,
      fromStatusId: 4, // Cancelled
      toStatusId: 6, // Backlog
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move cancelled task to backlog',
    },
    // WaitForFeedback → ToDo (All roles can do this)
    {
      id: 21,
      fromStatusId: 5, // WaitForFeedback
      toStatusId: 1, // ToDo
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move task back to To Do from feedback state',
    },
    // WaitForFeedback → InProgress (All roles can do this)
    {
      id: 22,
      fromStatusId: 5, // WaitForFeedback
      toStatusId: 2, // InProgress
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Resume work after receiving feedback',
    },
    // WaitForFeedback → Completed (Only Owner can do this)
    {
      id: 23,
      fromStatusId: 5, // WaitForFeedback
      toStatusId: 3, // Completed
      allowOwner: true,
      allowAdmin: false,
      allowMember: false,
      description: 'Mark a task waiting for feedback as completed',
    },
    // WaitForFeedback → Cancelled (All roles can do this)
    {
      id: 24,
      fromStatusId: 5, // WaitForFeedback
      toStatusId: 4, // Cancelled
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Cancel a task that is waiting for feedback',
    },
    // WaitForFeedback → Backlog (All roles can do this)
    {
      id: 25,
      fromStatusId: 5, // WaitForFeedback
      toStatusId: 6, // Backlog
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move task waiting for feedback to backlog',
    },
    // Backlog → ToDo (All roles can do this)
    {
      id: 26,
      fromStatusId: 6, // Backlog
      toStatusId: 1, // ToDo
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move task from backlog to active To Do',
    },
    // Backlog → InProgress (All roles can do this)
    {
      id: 27,
      fromStatusId: 6, // Backlog
      toStatusId: 2, // InProgress
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move task from backlog directly to in progress',
    },
    // Backlog → Completed (Only Owner can do this)
    {
      id: 28,
      fromStatusId: 6, // Backlog
      toStatusId: 3, // Completed
      allowOwner: true,
      allowAdmin: false,
      allowMember: false,
      description: 'Mark a backlog task as completed',
    },
    // Backlog → Cancelled (All roles can do this)
    {
      id: 29,
      fromStatusId: 6, // Backlog
      toStatusId: 4, // Cancelled
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Cancel a task from backlog',
    },
    // Backlog → WaitForFeedback (All roles can do this)
    {
      id: 30,
      fromStatusId: 6, // Backlog
      toStatusId: 5, // WaitForFeedback
      allowOwner: true,
      allowAdmin: true,
      allowMember: true,
      description: 'Move task from backlog to waiting for feedback',
    },
  ];

  const createdTaskStatusTransitions = [];

  for (const transition of taskStatusTransitions) {
    const createdTransition = await prisma.taskStatusTransition.upsert({
      where: { id: transition.id },
      update: {},
      create: transition,
    });
    createdTaskStatusTransitions.push(createdTransition);
  }

  console.log('Created task status transitions:', createdTaskStatusTransitions);

  // Create notification types
  const notificationTypes = [
    {
      id: 1,
      name: 'task_assigned',
      displayName: 'Task Assigned',
      description: 'Notification when a task is assigned to a user',
      template: 'You have been assigned a new task: {taskTitle}',
      color: '#3B82F6',
      isActive: true,
    },
    {
      id: 2,
      name: 'task_status_changed',
      displayName: 'Task Status Changed',
      description: 'Notification when a task status is changed',
      template: 'Task {taskTitle} status has been changed to {statusName}',
      color: '#10B981',
      isActive: true,
    },
    {
      id: 3,
      name: 'task_completed',
      displayName: 'Task Completed',
      description: 'Notification when a task is marked as completed',
      template: 'Task {taskTitle} has been completed',
      color: '#34D399',
      isActive: true,
    },
    {
      id: 4,
      name: 'points_earned',
      displayName: 'Points Earned',
      description: 'Notification when a user earns points',
      template: 'You have earned {points} points for completing task {taskTitle}',
      color: '#F59E0B',
      isActive: true,
    },
    {
      id: 5,
      name: 'department_joined',
      displayName: 'Department Joined',
      description: 'Notification when a user joins a department',
      template: 'You have been added to the {departmentName} department',
      color: '#8B5CF6',
      isActive: true,
    },
    {
      id: 6,
      name: 'task_progress_comment',
      displayName: 'Task Progress Comment',
      description: 'Notification when a comment is added to task progress',
      template: 'New comment on task {taskTitle}: {commentContent}',
      color: '#EC4899',
      isActive: true,
    },
    {
      id: 7,
      name: 'task_progress_request',
      displayName: 'Task Progress Request',
      description: 'Notification when a progress update is requested for a task',
      template: 'Progress update requested for task {taskTitle}',
      color: '#6366F1',
      isActive: true,
    },
  ];

  const createdNotificationTypes = [];

  for (const notificationType of notificationTypes) {
    const createdNotificationType = await prisma.notificationType.upsert({
      where: { id: notificationType.id },
      update: {},
      create: notificationType,
    });
    createdNotificationTypes.push(createdNotificationType);
  }

  console.log('Created notification types:', createdNotificationTypes);

  // Create assistant message types
  console.log('Seeding AssistantMessageTypes...');

  const assistantMessageTypes = [
    {
      name: 'System',
      description: 'System or instruction messages that provide context and guidelines to the AI assistant'
    },
    {
      name: 'User',
      description: 'Messages sent by users as input to the AI assistant'
    },
    {
      name: 'Assistant',
      description: 'Response messages generated by the AI assistant'
    }
  ];

  const createdAssistantMessageTypes = [];

  for (const messageType of assistantMessageTypes) {
    const createdMessageType = await prisma.assistantMessageType.upsert({
      where: { name: messageType.name },
      update: {
        description: messageType.description
      },
      create: {
        name: messageType.name,
        description: messageType.description
      }
    });
    createdAssistantMessageTypes.push(createdMessageType);
    console.log(`✓ Created/Updated AssistantMessageType: ${messageType.name}`);
  }

  console.log('Created assistant message types:', createdAssistantMessageTypes);

  // Create bot user
  console.log('Seeding Bot User...');

  const botUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      passwordHash: await bcrypt.hash('12341234', 10),
      userRoleId: 4, // Bot role ID
    },
    create: {
      email: '<EMAIL>',
      passwordHash: await bcrypt.hash('12341234', 10),
      firstName: 'Bot',
      lastName: 'Assistant',
      userRoleId: 4, // Bot role ID
    },
  });

  console.log('✓ Created/Updated Bot User:', botUser.email);

  // Create feedback types
  console.log('Seeding Feedback Types...');

  const feedbackTypes = [
    {
      id: 1,
      name: 'private',
      displayName: 'Private Feedback',
      description: 'Private feedback between individuals within the same organization',
    },
    {
      id: 2,
      name: 'task',
      displayName: 'Task Feedback',
      description: 'Feedback related to specific task performance and collaboration',
    },
    {
      id: 3,
      name: 'department',
      displayName: 'Department Feedback',
      description: 'Feedback for department members and cross-departmental collaboration',
    },
    {
      id: 4,
      name: 'organization',
      displayName: 'Organization Feedback',
      description: 'Organization-wide feedback for all members',
    },
  ];

  const createdFeedbackTypes = [];

  for (const feedbackType of feedbackTypes) {
    const createdFeedbackType = await prisma.feedbackType.upsert({
      where: { id: feedbackType.id },
      update: {
        displayName: feedbackType.displayName,
        description: feedbackType.description,
      },
      create: feedbackType,
    });
    createdFeedbackTypes.push(createdFeedbackType);
    console.log(`✓ Created/Updated FeedbackType: ${feedbackType.name}`);
  }

  console.log('Created feedback types:', createdFeedbackTypes);

  // All demo data for users, organizations, departments, tasks, notifications, and point transactions has been removed
  // Only keeping configuration data like roles, statuses, and types
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async e => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
